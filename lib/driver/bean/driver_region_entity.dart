import 'package:connect/generated/json/driver_region_entity.g.dart';

import 'package:connect/data/multi_name_entity.dart';
import 'package:connect/generated/json/base/json_field.dart';

@JsonSerializable()
class DriverRegionEntity {

	DriverRegionEntity();

	factory DriverRegionEntity.fromJson(Map<String, dynamic> json) => $DriverRegionEntityFromJson(json);

	Map<String, dynamic> toJson() => $DriverRegionEntityToJson(this);

	@JSONField(name: "_id")
	String? regionId;
	String? name;
	DriverRegionLocation? location;
	String? timezone;
	double? tax;
	String? updatedAt;
	int? restriction;
	DriverRegionShift? shift;
	int? level3Spot;
	DriverRegionBundle? bundle;
	bool? vipBanner;
	Tron? tron;
}

@JsonSerializable()
class Tron {
	Tron();

	factory Tron.fromJson(Map<String, dynamic> json) => $TronFromJson(json);

	Map<String, dynamic> toJson() => $TronToJson(this);

	TronOptions? options;
}

@JsonSerializable()
class TronOptions {
	TronOptions();

	factory TronOptions.fromJson(Map<String, dynamic> json) => $TronOptionsFromJson(json);

	Map<String, dynamic> toJson() => $TronOptionsToJson(this);

	List<String>? rejectReasons;
}

@JsonSerializable()
class DriverRegionLocation {

	DriverRegionLocation();

	factory DriverRegionLocation.fromJson(Map<String, dynamic> json) => $DriverRegionLocationFromJson(json);

	Map<String, dynamic> toJson() => $DriverRegionLocationToJson(this);

	List<double>? coordinates;
	String? type;
}

@JsonSerializable()
class DriverRegionShift {

	DriverRegionShift();

	factory DriverRegionShift.fromJson(Map<String, dynamic> json) => $DriverRegionShiftFromJson(json);

	Map<String, dynamic> toJson() => $DriverRegionShiftToJson(this);

	List<MultiNameEntity>? motd;
}

@JsonSerializable()
class DriverRegionBundle {

	DriverRegionBundle();

	factory DriverRegionBundle.fromJson(Map<String, dynamic> json) => $DriverRegionBundleFromJson(json);

	Map<String, dynamic> toJson() => $DriverRegionBundleToJson(this);

	double? distance;
}
