import 'package:connect/common/MyStyles.dart';
import 'package:connect/common/global.dart';
import 'package:connect/driver/driver_main_tab.dart';
import 'package:connect/driver/provider/driver_model.dart';
import 'package:connect/driver/provider/driver_region_announcement_model.dart';
import 'package:connect/event/login_event.dart';
import 'package:connect/ui/view/empty_container.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/utils/location_manager.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'bean/driver_orders_entity.dart';
import 'delivery/driver_region_announcement.dart';
import 'driver_online_switch.dart';
import 'driver_order.dart';
import 'package:provider/provider.dart';

import 'driver_profile_card.dart';

/// driver page
class DriverRoute extends StatefulWidget {
  DriverRoute({Key? key}) : super(key: key);

  @override
  _DriverRouteState createState() => _DriverRouteState();
}

class _DriverRouteState extends State<DriverRoute>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late RefreshController _refreshController;

  @override
  void initState() {
    super.initState();
    context.read<DriverModel>().driverOrderEntity = null;
    _refreshController = RefreshController(initialRefresh: false);

    ///开启location定位监听
    LocationManager.startLocationListening();

    ///每次启动检查是否需要上传日志
    LogManager.instance!.uploadLog(false);

    //create order
    GlobalConfig.eventBus.on<DriverCreateOrder>().listen((event) async {
      if (!mounted) return;
      LoadingUtils.show();
      // await Future.delayed(Duration(seconds: 10));
      await context.read<DriverModel>().createOrder();
      LoadingUtils.dismiss();
    });
  }

  @override
  void dispose() {
    super.dispose();
    LocationManager.cancelLocationListening();
  }

  void _onRefresh() {
    context.read<DriverRegionAnnouncementModel>().regionAnnouncement();
    context
        .read<DriverModel>()
        .driverOrders(loading: false)
        .whenComplete(() => _refreshController.refreshCompleted());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    DriverOrdersEntity? entity = context.watch<DriverModel>().driverOrderEntity;
    bool isNoOrder = false;
    bool isNewOrder = false;
    //init isNext value
    if (entity != null && entity.next != null) {
      entity.next!.isNext = true;
    }
    if (entity == null ||
        ((entity.route == null || entity.route!.isEmpty) &&
            entity.next == null)) {
      isNoOrder = true;
    } else if (entity.next == null &&
        (entity.route != null && entity.route!.isNotEmpty)) {
      isNewOrder = true;
    }

    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          title: DriverOnlineSwitch(entity),
        ),
        backgroundColor: MyColors.bg,
        resizeToAvoidBottomInset: false,
        body: SmartRefresher(
          controller: _refreshController,
          physics: AlwaysScrollableScrollPhysics(),
          onRefresh: _onRefresh,
          child: isNoOrder || isNewOrder
              ? _buildEmpty(entity, isNoOrder, isNewOrder)
              : _buildDriverFlow(entity),
        ));
  }

  Widget _buildEmpty(
      DriverOrdersEntity? entity, bool isNoOrder, bool isNewOrder) {
    return Column(
      children: [
        ListView(
          shrinkWrap: true,
          children: [
            DriverProfileCard(
              entity: entity,
            ),
            DriverRegionAnnouncement(),
          ],
        ),
        Expanded(child: DriverOrder(entity, isNoOrder, isNewOrder)),
      ],
    );
  }

  Widget _buildDriverFlow(DriverOrdersEntity? entity) {
    return ListView(
      shrinkWrap: true,
      children: [
        DriverProfileCard(
          entity: entity,
        ),
        DriverRegionAnnouncement(),
        Container(
          child: DriverMainTab(entity),
        ),
      ],
    );
  }
}
