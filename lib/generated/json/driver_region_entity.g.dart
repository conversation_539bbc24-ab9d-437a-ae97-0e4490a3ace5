import 'package:connect/generated/json/base/json_convert_content.dart';
import 'package:connect/driver/bean/driver_region_entity.dart';
import 'package:connect/data/multi_name_entity.dart';


DriverRegionEntity $DriverRegionEntityFromJson(Map<String, dynamic> json) {
	final DriverRegionEntity driverRegionEntity = DriverRegionEntity();
	final String? regionId = jsonConvert.convert<String>(json['_id']);
	if (regionId != null) {
		driverRegionEntity.regionId = regionId;
	}
	final String? name = jsonConvert.convert<String>(json['name']);
	if (name != null) {
		driverRegionEntity.name = name;
	}
	final DriverRegionLocation? location = jsonConvert.convert<DriverRegionLocation>(json['location']);
	if (location != null) {
		driverRegionEntity.location = location;
	}
	final String? timezone = jsonConvert.convert<String>(json['timezone']);
	if (timezone != null) {
		driverRegionEntity.timezone = timezone;
	}
	final double? tax = jsonConvert.convert<double>(json['tax']);
	if (tax != null) {
		driverRegionEntity.tax = tax;
	}
	final String? updatedAt = jsonConvert.convert<String>(json['updatedAt']);
	if (updatedAt != null) {
		driverRegionEntity.updatedAt = updatedAt;
	}
	final int? restriction = jsonConvert.convert<int>(json['restriction']);
	if (restriction != null) {
		driverRegionEntity.restriction = restriction;
	}
	final DriverRegionShift? shift = jsonConvert.convert<DriverRegionShift>(json['shift']);
	if (shift != null) {
		driverRegionEntity.shift = shift;
	}
	final int? level3Spot = jsonConvert.convert<int>(json['level3Spot']);
	if (level3Spot != null) {
		driverRegionEntity.level3Spot = level3Spot;
	}
	final DriverRegionBundle? bundle = jsonConvert.convert<DriverRegionBundle>(json['bundle']);
	if (bundle != null) {
		driverRegionEntity.bundle = bundle;
	}
	final bool? vipBanner = jsonConvert.convert<bool>(json['vipBanner']);
	if (vipBanner != null) {
		driverRegionEntity.vipBanner = vipBanner;
	}
	final Tron? tron = jsonConvert.convert<Tron>(json['tron']);
	if (tron != null) {
		driverRegionEntity.tron = tron;
	}
	return driverRegionEntity;
}

Map<String, dynamic> $DriverRegionEntityToJson(DriverRegionEntity entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['_id'] = entity.regionId;
	data['name'] = entity.name;
	data['location'] = entity.location?.toJson();
	data['timezone'] = entity.timezone;
	data['tax'] = entity.tax;
	data['updatedAt'] = entity.updatedAt;
	data['restriction'] = entity.restriction;
	data['shift'] = entity.shift?.toJson();
	data['level3Spot'] = entity.level3Spot;
	data['bundle'] = entity.bundle?.toJson();
	data['vipBanner'] = entity.vipBanner;
	data['tron'] = entity.tron?.toJson();
	return data;
}

DriverRegionLocation $DriverRegionLocationFromJson(Map<String, dynamic> json) {
	final DriverRegionLocation driverRegionLocation = DriverRegionLocation();
	final List<double>? coordinates = jsonConvert.convertListNotNull<double>(json['coordinates']);
	if (coordinates != null) {
		driverRegionLocation.coordinates = coordinates;
	}
	final String? type = jsonConvert.convert<String>(json['type']);
	if (type != null) {
		driverRegionLocation.type = type;
	}
	return driverRegionLocation;
}

Map<String, dynamic> $DriverRegionLocationToJson(DriverRegionLocation entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['coordinates'] =  entity.coordinates;
	data['type'] = entity.type;
	return data;
}

DriverRegionShift $DriverRegionShiftFromJson(Map<String, dynamic> json) {
	final DriverRegionShift driverRegionShift = DriverRegionShift();
	final List<MultiNameEntity>? motd = jsonConvert.convertListNotNull<MultiNameEntity>(json['motd']);
	if (motd != null) {
		driverRegionShift.motd = motd;
	}
	return driverRegionShift;
}

Map<String, dynamic> $DriverRegionShiftToJson(DriverRegionShift entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['motd'] =  entity.motd?.map((v) => v.toJson()).toList();
	return data;
}

DriverRegionBundle $DriverRegionBundleFromJson(Map<String, dynamic> json) {
	final DriverRegionBundle driverRegionBundle = DriverRegionBundle();
	final double? distance = jsonConvert.convert<double>(json['distance']);
	if (distance != null) {
		driverRegionBundle.distance = distance;
	}
	return driverRegionBundle;
}

Map<String, dynamic> $DriverRegionBundleToJson(DriverRegionBundle entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['distance'] = entity.distance;
	return data;
}

Tron $TronFromJson(Map<String, dynamic> json) {
	final Tron tron = Tron();
	final TronOptions? options = jsonConvert.convert<TronOptions>(json['options']);
	if (options != null) {
		tron.options = options;
	}
	return tron;
}

Map<String, dynamic> $TronToJson(Tron entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['options'] = entity.options?.toJson();
	return data;
}

TronOptions $TronOptionsFromJson(Map<String, dynamic> json) {
	final TronOptions tronOptions = TronOptions();
	final List<String>? rejectReasons = jsonConvert.convertListNotNull<String>(json['rejectReasons']);
	if (rejectReasons != null) {
		tronOptions.rejectReasons = rejectReasons;
	}
	return tronOptions;
}

Map<String, dynamic> $TronOptionsToJson(TronOptions entity) {
	final Map<String, dynamic> data = <String, dynamic>{};
	data['rejectReasons'] = entity.rejectReasons;
	return data;
}