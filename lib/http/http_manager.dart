import 'dart:convert';
import 'dart:io';
import 'package:connect/common/global.dart';
import 'package:connect/ui/login/login_route.dart';
import 'package:connect/ui/view/loading.dart';
import 'package:connect/ui/view/message_dialog.dart';
import 'package:connect/utils/connect_cache.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:connect/utils/log_util.dart';
import 'package:connect/utils/log_manager.dart';
import 'package:dio/io.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'commom/http_uri.dart';
import 'http_error.dart';
import 'interceptors/connect_interceptors.dart';
import 'package:flutter/cupertino.dart';

class HttpManager {
  static const String TAG = "HttpManager";

  static const int CONNECT_TIMEOUT = 50 * 1000;
  static const int RECEIVE_TIMEOUT = 50 * 1000;

  factory HttpManager() => _getInstance()!;

  static HttpManager? get instance => _getInstance();
  static HttpManager? _instance;
  static late Dio _dio;
  late BaseOptions options;

  Dio get dio => _dio;

  HttpManager._internal() {
    BaseOptions options = BaseOptions(
        connectTimeout: Duration(milliseconds: CONNECT_TIMEOUT),
        receiveTimeout: Duration(milliseconds: RECEIVE_TIMEOUT),
        baseUrl: GlobalConfig.isRelease ? HttpUri.PROD_URL : HttpUri.DEV_URL);

    _dio = Dio(options);

    _dio.interceptors.add(ConnectInterceptor());
    // if (!ConnectCache.isRelease())
    _dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
    // _dio.interceptors.add(DioLogInterceptor());
    _dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
      final client = HttpClient();

      // Set up the proxy for debugging if not in release mode
      if (!GlobalConfig.isRelease) {
        // Uncomment the proxy you need
        // client.findProxy = (uri) => "PROXY *************:10088";
        client.findProxy = (uri) => "PROXY **************:10088";
      }

      // Bypass certificate validation (for development/debugging ONLY)
      client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;

      return client;
    });
    // (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (client) {
    //   if (!GlobalConfig.isRelease) {
    //     // client.findProxy = (uri) => "PROXY **************:8888";
    //     // client.findProxy = (uri) => "PROXY *************:8888";
    //     // client.findProxy = (uri) => "PROXY *************:8888";
    //     // client.findProxy = (uri) => "PROXY **************:8888";
    //   }
    //   client.badCertificateCallback =
    //       (X509Certificate cert, String host, int port) => true;
    // };
  }

  static HttpManager? _getInstance() {
    if (_instance == null) {
      _instance = HttpManager._internal();
    }
    return _instance;
  }

  Future<Response?> get(api,
      {params,
      option,
      showMsg = true,
      withLoading = true,
      ValueChanged? callback}) {
    return doRequest("get", api,
        params: params,
        option: option,
        showMsg: showMsg,
        withLoading: withLoading,
        callback: callback);
  }

  Future<Response?> put(api,
      {data,
      option,
      showMsg = true,
      withLoading = true,
      ValueChanged? callback}) {
    return doRequest("put", api,
        params: data,
        option: option,
        showMsg: showMsg,
        withLoading: withLoading,
        callback: callback);
  }

  Future<Response?> getUri(api,
      {params,
      option,
      showMsg = true,
      withLoading = true,
      ValueChanged? callback}) {
    return doRequest("getUri", api,
        params: params,
        option: option,
        showMsg: showMsg,
        withLoading: withLoading,
        callback: callback);
  }

  Future<Response?> post(api,
      {params,
      option,
      showMsg = true,
      withLoading = true,
      ValueChanged? callback}) {
    return doRequest("post", api,
        params: params,
        option: option,
        showMsg: showMsg,
        withLoading: withLoading,
        callback: callback);
  }

  Future<Response?> delete(api,
      {params,
      option,
      showMsg = true,
      withLoading = true,
      ValueChanged? callback}) {
    return doRequest("delete", api,
        params: params,
        option: option,
        showMsg: showMsg,
        withLoading: withLoading,
        callback: callback);
  }

  Future<Response?> doRequest(String method, api,
      {params,
      option,
      showMsg = true,
      withLoading = true,
      ValueChanged? callback}) async {
    try {
      if (withLoading) LoadingUtils.show();
      switch (method) {
        case "get":
          return await _dio.get(api, queryParameters: params, options: option);
        case "put":
          return await _dio.put(api, data: params, options: option);
        case "getUri":
          return await _dio.getUri(Uri(path: api, queryParameters: params),
              options: option);
        case "post":
          return await _dio.post(api, data: params, options: option);
        case "delete":
          return await _dio.delete(api, data: params, options: option);
      }
    } on DioError catch (e) {
      //submit exception info
      LogUtil.v("method::$method" +
          ",url::$api" +
          ",params::$params" +
          ",e::${e.toString()}");

      //exception callback
      if (callback != null) callback(e);

      var extraMaps = Map<String, dynamic>();

      String? errorMessage = HttpError.errorHandler(e);
      //collect why occur unExcepted error
      if (errorMessage != null &&
          errorMessage.contains(HttpError.UNEXPECTED_ERROR)) {
        var split = errorMessage.split("::");
        errorMessage = "${split[0]}";
        extraMaps["errorMessage"] = "${split[1]}";
      }

      var connectivityResult = await (Connectivity().checkConnectivity());
      if (connectivityResult != ConnectivityResult.none) {
        extraMaps["method"] = method;
        extraMaps["params"] = params;
        extraMaps["url"] = "$api";
        extraMaps["netWork"] = "${connectivityResult.toString()}";
        extraMaps["errorType"] = "${e.type.toString()}";
        if (e.response != null) {
          extraMaps["response"] = "${e.response.toString()}";
          extraMaps["statusCode"] = "${e.response!.statusCode}";
          extraMaps["statusMessage"] = "${e.response!.statusMessage}";
        }
        extraMaps["time"] = DateTime.now().toString();

        var sentryEvent = SentryEvent(
            extra: extraMaps, message: SentryMessage("$errorMessage"));
        Sentry.captureEvent(sentryEvent);

        // persistent log
        LogManager.instance!.log(TAG, "DioError", jsonEncode(extraMaps));
      }

      //exception dialog
      if (showMsg) {
        MessageDialog.messageAlert(errorMessage, ok: () {
          if (e.response != null &&
              e.response!.statusCode == HttpError.UNAUTHORIZED) {
            ConnectCache.clearCurrentRoleId();
            ConnectCache.clearToken();
            Navigator.pushNamedAndRemoveUntil(
                GlobalConfig.navigatorKey.currentContext!,
                LoginRoute.tag,
                (route) => false);
          }
        });
      }
      throw e;
    }
    return null;
  }
}
