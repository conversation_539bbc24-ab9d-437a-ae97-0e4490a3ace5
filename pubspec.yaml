name: connect
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 4.8.2+98

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_sticky_header: ^0.7.0
  sp_util: ^2.0.3
  url_launcher: ^6.0.12
  flutter_libphonenumber: ^2.5.1
  phone_numbers_parser: ^9.0.3
  provider: ^6.0.1
  dio: ^5.8.0+1
  curl_logger_dio_interceptor: ^1.0.0
  connectivity_plus: ^6.1.4
  fluttertoast: ^8.0.8
  package_info_plus: 8.3.0
  device_info_plus: ^11.3.0
  event_bus: ^2.0.0
  audioplayers: ^6.5.0
  intl: ^0.19.0
  table_calendar: ^3.0.3
  date_format: ^2.0.4
  pull_to_refresh: ^2.0.0
  sprintf: ^7.0.0
  flutter_picker_plus: ^1.5.2
  printing: ^5.6.0
  pdf: ^3.6.1
  flutter_smart_dialog: ^4.9.8+8
  timezone: ^0.10.1
  geolocator: ^13.0.4
  sentry_flutter: ^9.3.0
  wakelock_plus: 1.3.2
  google_maps_flutter: ^2.1.0

  flutter_staggered_grid_view: ^0.7.0
  archive: ^4.0.7
  permission_handler: ^12.0.1
  launch_app_store: ^1.1.4
  amplitude_flutter: ^4.3.3
  flutter_logs: ^2.2.1
  path_provider: ^2.0.6
  flutter_email_sender: ^7.0.0
  map_launcher: ^3.5.0
  json_annotation: ^4.4.0
  # web
  flutter_easyloading: ^3.0.0
  uuid: ^4.5.1
  flutter_svg: ^2.2.0
  auto_size_text: ^3.0.0

  webview_flutter: ^4.13.0

  wechat_assets_picker: 9.5.1
  wechat_camera_picker: 4.3.7

#  location:
#    git:
#      url: http://github.com/PieterAelse/flutterlocation.git
#      ref: issue_639/build_on_and_support_android_12_sdk_31
#      path: packages/location # the git repository package directory
# pay attention here https://stackoverflow:com/questions/75273823/java-lang-incompatibleclasschangeerror-found-interface-com-google-android-gms-l:
  location: ^8.0.1

  firebase_performance: ^0.10.1+9
  firebase_core: ^3.15.1
  firebase_crashlytics: ^4.3.9
  firebase_analytics: ^11.5.2
  firebase_messaging: ^15.2.9
  firebase_app_installations: ^0.3.2+9


# upgrade WeChat camera and upgrade flutter sdk
#dependency_overrides:
#  photo_manager: ^3.2.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  json_serializable: ^6.1.6
  build_runner: ^2.1.9
  intl_utils: ^2.8.5


#dependency_overrides:
#  firebase_core_platform_interface: 4.5.1

flutter_icons:
  android: "ic_launcher"
  ios: true
  image_path: "assets/icon/ic_launcher.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/order.mp3
    - assets/config/tags_en.json
    - assets/config/tags_zh.json
    - assets/config/tags_hk.json
    - assets/config/cancel_order_en.json
    - assets/config/cancel_order_zh.json
    - assets/config/cancel_order_hk.json
    - assets/heatmap/index.html
    - assets/heatmap/index.js
    - assets/heatmap/lodash.js
    - assets/heatmap/style.css
    - assets/images/
    - assets/images/driver_tab/
    - assets/images/icons/
    - assets/images/icons/order/
    - assets/images/more/
    - assets/images/order/
    - assets/images/report/
    - assets/images/rest/
    - assets/images/tab/
    - assets/images/schedule/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true

flutter_gen:
  output: lib/gen/ # Optional (default: lib/gen/)
  line_length: 80 # Optional (default: 80)

  # Optional
  integrations:
    flutter_svg: true
